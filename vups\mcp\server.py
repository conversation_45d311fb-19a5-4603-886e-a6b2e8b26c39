import os
from vups import logger
from vups.algos.tools.video_dump import video_source_downloader
from datetime import datetime
import tabulate as tb
from bilibili_api import video, Credential, search
from mcp.server.fastmcp import FastMCP

from vups.algos.tools.asr import AliParaformerASR, BCutASR
from vups.algos.tools.subtitle_task_manager import task_manager
from vups_server.base.cookie_manager import get_cookie_field
from vups_server.base.response_schema import create_success_response, create_error_response
from vups.utils import get_user_mid, get_user_fullname

# USING ENV
SESSDATA = os.getenv('sessdata')
BILI_JCT = os.getenv('bili_jct')
BUVID3 = os.getenv('buvid3')

if not all([SESSDATA, BILI_JCT, BUVID3]):
    SESSDATA = get_cookie_field("user", "SESSDATA")
    BILI_JCT = get_cookie_field("user", "bili_jct")
    BUVID3 = get_cookie_field("user", "buvid3")
credential = Credential(sessdata=SESSDATA, bili_jct=BILI_JCT, buvid3=BUVID3)

mcp = FastMCP("vups-mcp")

# --------------------------------------- BASIC ---------------------------------------
@mcp.tool("get_user_current_follower", description="获取up主当前粉丝数，需提供up主名称")
async def get_user_current_follower(char: str) -> dict:
    """
    char: up主名称
    """
    try:
        mid = get_user_mid(vtuber)
        vtuber = get_user_fullname(mid)
        follower_count = await query_now_user_follower_num_by_mid(mid)
        return create_success_response(
            data={
                "vtuber": vtuber,
                "mid": mid,
                "result": follower_count,
                "timestamp": datetime.now().isoformat()
            },
            message="Current follower count retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get current followers for Vup {vtuber}: {e}")
        raise (
            create_error_response("Failed to retrieve follower count", 500)
        )


# --------------------------------------- BASIC ---------------------------------------

# --------------------------------------- VIDEO ---------------------------------------
asr = AliParaformerASR(
    api_key=os.getenv("DASHSCOPE_API_KEY"),
    streaming_mode=True,
)

# asr = BCutASR()

@mcp.tool("search_video", description="搜索bilibili视频")
async def search_video(keyword: str, page: int = 1, page_size: int = 20) -> str:
    """
    keyword: 搜索关键词
    page: 页码，默认1
    page_size: 每页数量，默认20
    """
    search_result = await search.search_by_type(keyword, search_type=search.SearchObjectType.VIDEO, page=page, page_size=page_size)

    table_data = []
    headers = ["发布日期", "标题", "UP主", "时长", "播放量", "点赞数", "类别", "bvid"]

    for video in search_result["result"]:
        pubdate = datetime.fromtimestamp(video["pubdate"]).strftime("%Y/%m/%d")

        title_link = f"[{video['title']}]({video['arcurl']})"

        table_data.append([
            pubdate,
            title_link,
            video["author"],
            video["duration"],
            video["play"],
            video["like"],
            video["typename"],
            video["bvid"]
        ])

    return tb.tabulate(table_data, headers=headers, tablefmt="pipe")

@mcp.tool("get_video_subtitle", description="获取bilibili视频的字幕，需提供视频bvid")
async def get_video_subtitle(bvid: str) -> dict:
    """
    bvid: bvid
    """
    return await video_source_downloader.download_subtitle(bvid=bvid, output_filename="tests/vups/data/test_subtitle.txt")

@mcp.tool("get_video_info", description="获取bilibili视频信息，需提供视频BV号")
async def get_video_info(bvid: str) -> dict:
    """
    bvid: 视频BV号
    """
    v = video.Video(bvid=bvid, credential=credential)
    info = await v.get_info()
    return info

@mcp.tool("get_media_subtitle", description="获取媒体文件的AI中文字幕，需提供媒体文件URL")
async def get_media_subtitle(url: str) -> dict:
    """
    url: 媒体文件URL
    """
    asr_data = await asr.transcribe([url])
    return asr_data
# --------------------------------------- VIDEO ---------------------------------------






# @mcp.tool("start_video_subtitle_task", description="开始bilibili视频字幕生成任务（异步处理，适合长视频）")
# async def start_video_subtitle_task(bvid: str, output_filename: str = None) -> dict:
#     """
#     bvid: 视频BV号
#     output_filename: 输出文件名（可选）
#     返回: {"task_id": "任务ID", "status": "pending", "message": "任务已创建"}
#     """
#     task_id = task_manager.create_task(bvid, output_filename)
#     return {
#         "task_id": task_id,
#         "status": "pending",
#         "message": f"Subtitle generation task created for video {bvid}. Use get_subtitle_task_status to check progress."
#     }

# @mcp.tool("get_subtitle_task_status", description="查询字幕生成任务状态和进度")
# async def get_subtitle_task_status(task_id: str) -> dict:
#     """
#     task_id: 任务ID
#     返回: 任务状态、进度百分比、当前步骤等信息
#     """
#     progress = task_manager.get_task_status(task_id)
#     if not progress:
#         return {"error": "Task not found", "task_id": task_id}

#     return {
#         "task_id": task_id,
#         "status": progress.status.value,
#         "progress_percent": progress.progress_percent,
#         "current_step": progress.current_step,
#         "estimated_remaining": progress.estimated_remaining,
#         "error_message": progress.error_message,
#         "created_at": progress.created_at.isoformat() if progress.created_at else None,
#         "updated_at": progress.updated_at.isoformat() if progress.updated_at else None
#     }

# @mcp.tool("get_subtitle_task_result", description="获取已完成的字幕生成任务结果")
# async def get_subtitle_task_result(task_id: str) -> dict:
#     """
#     task_id: 任务ID
#     返回: 任务结果（仅当任务完成时）
#     """
#     progress = task_manager.get_task_status(task_id)
#     if not progress:
#         return {"error": "Task not found", "task_id": task_id}

#     if progress.status.value != "completed":
#         return {
#             "error": f"Task not completed. Current status: {progress.status.value}",
#             "task_id": task_id,
#             "status": progress.status.value
#         }

#     return {
#         "task_id": task_id,
#         "status": "completed",
#         "result": progress.result,
#         "completed_at": progress.updated_at.isoformat() if progress.updated_at else None
#     }

# @mcp.tool("cancel_subtitle_task", description="取消正在进行的字幕生成任务")
# async def cancel_subtitle_task(task_id: str) -> dict:
#     """
#     task_id: 任务ID
#     返回: 取消操作结果
#     """
#     success = task_manager.cancel_task(task_id)
#     if success:
#         return {"task_id": task_id, "status": "cancelled", "message": "Task cancelled successfully"}
#     else:
#         return {"task_id": task_id, "error": "Failed to cancel task or task not found"}


mcp.run()
